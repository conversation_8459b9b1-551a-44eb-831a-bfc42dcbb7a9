// Sales Order Inventory Analysis component for MRP Dashboard
import { connectionManager } from '../../core/connection.js';

export class SalesOrderInventoryComponent {
  constructor(container, salesOrderData = null) {
    this.container = container;
    this.salesOrderData = salesOrderData;
    this.inventoryItems = [];
    this.filteredItems = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'InventoryID';
    this.sortDirection = 'asc';
    this.filterType = 'all'; // 'all', 'main', 'bom'
    this.isLoading = true;
    this.dbName = 'salesOrderInventoryDb';
    this.storeName = 'inventoryAnalysis';
    this.settingsStoreName = 'appSettings';
    this.dataSource = null;
    this.lastSyncTime = null;
  }

  async init() {
    console.log("Initializing Sales Order Inventory Analysis component");

    this.isLoading = true;
    this.render();

    try {
      await this.initDatabase();

      // Process the provided sales order data
      if (this.salesOrderData) {
        this.processInventoryData(this.salesOrderData);
      }

      this.isLoading = false;
      this.render();
    } catch (error) {
      console.error("Error initializing inventory analysis:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      const checkRequest = indexedDB.open(this.dbName);

      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        let needsUpgrade = false;

        if (!db.objectStoreNames.contains(this.storeName)) {
          needsUpgrade = true;
        }
        if (!db.objectStoreNames.contains(this.settingsStoreName)) {
          needsUpgrade = true;
        }

        const currentVersion = db.version;
        db.close();

        if (!needsUpgrade) {
          console.log("Inventory analysis database already exists with correct schema, version:", currentVersion);
          resolve();
          return;
        }

        const request = indexedDB.open(this.dbName, currentVersion + 1);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open inventory analysis database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened inventory analysis database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Upgrading inventory analysis database schema to version", db.version);

          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            store.createIndex("InventoryID", "InventoryID", { unique: false });
            store.createIndex("Type", "Type", { unique: false });
            store.createIndex("OrderNbr", "OrderNbr", { unique: false });
            console.log("Created inventory analysis store");
          }

          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
            console.log("Created settings store");
          }
        };
      };

      checkRequest.onerror = (event) => {
        console.error("Error checking database:", event.target.error);
        const freshRequest = indexedDB.open(this.dbName, 1);

        freshRequest.onerror = (event) => {
          console.error("Error creating new database:", event.target.error);
          reject(new Error("Could not create inventory analysis database"));
        };

        freshRequest.onsuccess = (event) => {
          console.log("Successfully created fresh inventory analysis database");
          resolve();
        };

        freshRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Creating new inventory analysis database schema");

          const store = db.createObjectStore(this.storeName, { keyPath: "id" });
          store.createIndex("InventoryID", "InventoryID", { unique: false });
          store.createIndex("Type", "Type", { unique: false });
          store.createIndex("OrderNbr", "OrderNbr", { unique: false });

          db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          console.log("Inventory analysis database schema created");
        };
      };
    });
  }

  processInventoryData(salesOrder) {
    if (!salesOrder || !Array.isArray(salesOrder.LineItems)) {
      console.warn('Invalid sales order data for inventory analysis');
      this.inventoryItems = [];
      this.filteredItems = [];
      return;
    }

    const processedItems = [];
    const uniquePartsSet = new Set();

    // Process each line item
    salesOrder.LineItems.forEach((lineItem, lineIndex) => {
      // Add main inventory item
      const mainItem = {
        id: `main-${lineIndex}-${Date.now()}`,
        OrderNbr: salesOrder.OrderNbr,
        Type: 'Main Item',
        InventoryID: lineItem.InventoryID || 'N/A',
        Description: lineItem.LineDescription || 'No description',
        Quantity: lineItem.Quantity || 0,
        UOM: lineItem.UOM || '',
        ProductionNumber: '',
        BOMCount: lineItem.BOMCount || 0,
        HasProduction: lineItem.HasProduction || false,
        IsMainItem: true,
        ParentLineItem: lineIndex + 1
      };

      processedItems.push(mainItem);
      uniquePartsSet.add(mainItem.InventoryID);

      // Process BOM items if they exist
      if (Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0) {
        lineItem.BOMItems.forEach((bomItem, bomIndex) => {
          const bomComponent = {
            id: `bom-${lineIndex}-${bomIndex}-${Date.now()}`,
            OrderNbr: salesOrder.OrderNbr,
            Type: 'BOM Component',
            InventoryID: bomItem.InventoryID || 'N/A',
            Description: bomItem.note || 'BOM Component',
            Quantity: '', // BOM items typically don't have quantities
            UOM: '',
            ProductionNumber: bomItem.ProductionNbr || '',
            BOMCount: 0,
            HasProduction: false,
            IsMainItem: false,
            ParentLineItem: lineIndex + 1,
            RowNumber: bomItem.rowNumber || bomIndex + 1
          };

          processedItems.push(bomComponent);
          if (bomComponent.InventoryID !== 'N/A') {
            uniquePartsSet.add(bomComponent.InventoryID);
          }
        });
      }
    });

    this.inventoryItems = processedItems;
    this.filteredItems = [...processedItems];
    this.calculateTotalPages();

    // Store analysis summary
    this.analysisSummary = {
      totalLineItems: salesOrder.LineItems.length,
      itemsWithBOM: salesOrder.LineItems.filter(item => 
        Array.isArray(item.BOMItems) && item.BOMItems.length > 0
      ).length,
      totalBOMComponents: processedItems.filter(item => !item.IsMainItem).length,
      uniqueParts: uniquePartsSet.size,
      totalItems: processedItems.length
    };

    console.log('Processed inventory analysis:', this.analysisSummary);
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredItems.length / this.itemsPerPage));
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.inventoryItems];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(item => {
        return (
          item.InventoryID.toLowerCase().includes(searchLower) ||
          item.Description.toLowerCase().includes(searchLower) ||
          item.ProductionNumber.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply type filter
    if (this.filterType !== 'all') {
      if (this.filterType === 'main') {
        filtered = filtered.filter(item => item.IsMainItem);
      } else if (this.filterType === 'bom') {
        filtered = filtered.filter(item => !item.IsMainItem);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[this.sortField] || '';
      let bValue = b[this.sortField] || '';

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredItems = filtered;
    this.calculateTotalPages();
    this.currentPage = 1;
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Processing inventory analysis...</p>
      </div>
    `;
  }

  renderContent() {
    if (!this.analysisSummary) {
      this.container.innerHTML = `
        <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 dark:bg-yellow-900 dark:border-yellow-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-yellow-500 dark:text-yellow-400">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-800 dark:text-yellow-200">
                No sales order data available for inventory analysis.
              </p>
            </div>
          </div>
        </div>
      `;
      return;
    }

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">
            Inventory Analysis - ${this.salesOrderData?.OrderNbr || 'Unknown Order'}
          </h2>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div class="text-sm font-medium text-blue-600 dark:text-blue-400">Line Items</div>
            <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">${this.analysisSummary.totalLineItems}</div>
          </div>
          <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div class="text-sm font-medium text-green-600 dark:text-green-400">With BOM</div>
            <div class="text-2xl font-bold text-green-900 dark:text-green-100">${this.analysisSummary.itemsWithBOM}</div>
          </div>
          <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div class="text-sm font-medium text-purple-600 dark:text-purple-400">BOM Components</div>
            <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">${this.analysisSummary.totalBOMComponents}</div>
          </div>
          <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <div class="text-sm font-medium text-orange-600 dark:text-orange-400">Unique Parts</div>
            <div class="text-2xl font-bold text-orange-900 dark:text-orange-100">${this.analysisSummary.uniqueParts}</div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700/20 p-4 rounded-lg">
            <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Items</div>
            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">${this.analysisSummary.totalItems}</div>
          </div>
        </div>

        <!-- Filters and Search -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-4 gap-4">
          <div class="flex items-center gap-2">
            <input
              type="text"
              id="inventory-search"
              placeholder="Search inventory..."
              class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
              value="${this.searchTerm || ''}"
            >
            <select id="type-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterType === 'all' ? 'selected' : ''}>All Items</option>
              <option value="main" ${this.filterType === 'main' ? 'selected' : ''}>Main Items Only</option>
              <option value="bom" ${this.filterType === 'bom' ? 'selected' : ''}>BOM Components Only</option>
            </select>
          </div>
        </div>

        <!-- Inventory Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Type">
                  Type <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="InventoryID">
                  Inventory ID <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Description">
                  Description <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Quantity">
                  Quantity <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ProductionNumber">
                  Production # <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Line #
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderTableRows()}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        ${this.renderPagination()}
      </div>
    `;

    this.setupEventListeners();
  }

  renderTableRows() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageItems = this.filteredItems.slice(startIndex, endIndex);

    if (pageItems.length === 0) {
      return `
        <tr>
          <td colspan="6" class="px-3 py-8 text-center text-gray-500 dark:text-gray-400">
            No inventory items found
          </td>
        </tr>
      `;
    }

    return pageItems.map(item => `
      <tr class="${item.IsMainItem ? 'bg-blue-50 dark:bg-blue-900/10' : 'bg-gray-50 dark:bg-gray-800/50'}">
        <td class="px-3 py-2 whitespace-nowrap">
          <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
            item.IsMainItem
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
          }">
            ${this.escapeHtml(item.Type)}
          </span>
        </td>
        <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
          ${this.escapeHtml(item.InventoryID)}
        </td>
        <td class="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
          ${this.escapeHtml(item.Description)}
        </td>
        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
          ${item.Quantity ? `${item.Quantity} ${item.UOM}`.trim() : '-'}
        </td>
        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
          ${this.escapeHtml(item.ProductionNumber || '-')}
        </td>
        <td class="px-3 py-2 whitespace-nowrap text-sm text-center text-gray-900 dark:text-white">
          ${item.ParentLineItem}${item.RowNumber ? `.${item.RowNumber}` : ''}
        </td>
      </tr>
    `).join('');
  }

  renderPagination() {
    if (this.totalPages <= 1) {
      return '';
    }

    return `
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredItems.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredItems.length)} of
          ${this.filteredItems.length} results
        </div>

        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('inventory-search');
    if (searchInput) {
      let searchTimeout;
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchTerm = e.target.value;
          this.applyFilters();
          this.render();
        }, 300);
      });
    }

    // Type filter
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
      typeFilter.addEventListener('change', (e) => {
        this.filterType = e.target.value;
        this.applyFilters();
        this.render();
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination buttons
    const firstPageBtn = document.getElementById('first-page');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const lastPageBtn = document.getElementById('last-page');

    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }
  }

  showError(message) {
    if (this.container) {
      this.container.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    } else {
      console.error("Container not available to show error:", message);
    }
  }

  escapeHtml(text) {
    if (typeof text !== 'string') return text;
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}
